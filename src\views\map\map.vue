<template>
  <div class="mainContainer">
    <MapTop />
    <div id="cesium-viewer" ref="viewerDivRef"></div>
    <div class="layer_tool">
      <MapTool @initModel="initModel" v-if="isViewer" />
      <MapCoating class="mouseInfo" />
    </div>

    <div class="pointPopup">
      <PointEditPopup ref="pointEditRef" />
      <PointSelectPopup ref="pointselectRef" />
      <MarkPointListPopup ref="markPointListRef" />
      <RegionListPopup ref="regionListRef" />
      <RegionInfoPopup ref="regionInfoRef" />
      <modelInit ref="modelRef" />
      <SportModel ref="modelRef" />
      <LandAirCombat ref="landAirCombat" />
      <DefaultModel ref="modelRef" />
      <Dodaf ref="dodafRef" />
    </div>
    <RightClickMenu />
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onMounted, ref } from "vue";

import { useMarkPointsStore } from "@/store/markPoints";

import pointImage from "@/assets/images/point/default.png";

import { initMap } from "@/utils/initMap";

import MapCoating from "@/views/map/components/map-coating.vue";
import MapTop from "@/views/map/components/map-top.vue";

import mouseEvent from "@/components/mouse/mouseEvents.ts";
import MarkPointListPopup from "@/components/popup/MarkPointListPopup.vue";
import RegionInfoPopup from "@/components/popup/RegionInfoPopup.vue";
import RegionListPopup from "@/components/popup/RegionListPopup.vue";
import PointEditPopup from "@/components/popup/pointPopup.vue";
import RightClickMenu from "@/components/popup/rightClickMenu.vue";
import PointSelectPopup from "@/components/popup/selectPointPopup.vue";
import MapTool from "@/components/tool/MapTool.vue";
import Dodaf from "@/components/yanshi/dodaf.vue";
import modelInit from "@/components/yanshi/index.vue";
import SportModel from "@/components/yanshi/sport.vue";
import DefaultModel from "@/components/yanshi/yanshi.vue";

import tool from "~/src/components/tool/tool";
import LandAirCombat from "~/src/components/yanshi/landAirCombat.vue";

import {
  DEFAULT_MARKER_COLOR,
  MarkerTypeColorMap,
} from "@/constants/markerTypes";
import { beijingMarkers } from "@/data/beijingMarkers";
// @ts-ignore
import CesiumNavigation from "cesium-navigation-es6";
// import { UseCase } from "~/src/lib/case";
import { useCommonStore } from "~/src/store/common";
import bus from "~/src/utils/bus";

const commonStore = useCommonStore();
const pointselectRef = ref();
const pointEditRef = ref<InstanceType<typeof PointEditPopup>>();
const markPointListRef = ref<InstanceType<typeof MarkPointListPopup>>();
const regionListRef = ref();
const regionInfoRef = ref();
const dodafRef = ref();
const landAirCombat = ref();

//必须在挂载后引入cesium地图组件
const viewerDivRef = ref<HTMLDivElement>();
const modelRef = ref();

let isViewer = ref(false);

/**
 * 初始化地图
 */
const initCesiumNavigation = (viewer: any) => {
  let options: any = {};
  new CesiumNavigation(viewer, options);
};
const initModel = () => {
  modelRef.value.init();
};

// const initCase = (viewerMap: Cesium.Viewer) => {
//   const useCaseAll = new UseCase({
//     viewerMap,
//   });
//   useCaseAll.explode();
// };

// 初始化北京地区标记点
const initBeijingMarkers = () => {
  const markPointsStore = useMarkPointsStore();
  const viewer = window.viewer;

  // 清空现有标记点
  markPointsStore.clearAllMarkPoints();

  // 添加预设的北京地区标记点
  beijingMarkers.forEach((marker) => {
    // 添加到 store 中
    const newPoint = markPointsStore.addMarkPoint({
      name: marker.name,
      position: marker.position,
      offset: marker.offset, // 添加偏移量
      type: marker.type,
      description: marker.description,
      weeklyData: marker.weeklyData, // 添加过去一周的数据
      echartsData: marker.echartsData, // 添加适配Echarts的数据
      displayLevel: marker.displayLevel, // 添加显示层级
      visible: false, // 设置初始可见性为 false
      flag: marker.flag || "default", // 使用标记点自己的flag属性，如果没有则默认为"cn"
    });

    // 添加到地图上
    let position = Cesium.Cartesian3.fromDegrees(
      marker.position.lon,
      marker.position.lat,
      marker.position.alt || 0,
    );

    // 如果有偏移量，应用偏移
    if (marker.offset) {
      // 获取东北上坐标系
      const eastNorthUpMatrix =
        Cesium.Transforms.eastNorthUpToFixedFrame(position);

      // 创建偏移向量 (二维，Z设为0)
      const offsetVector = new Cesium.Cartesian3(
        marker.offset.x * 10, // 放大偏移效果，使其更明显
        marker.offset.y * 10,
        0, // 不使用Z偏移
      );

      // 应用偏移
      const offsetPosition = new Cesium.Cartesian3();
      Cesium.Matrix4.multiplyByPoint(
        eastNorthUpMatrix,
        offsetVector,
        offsetPosition,
      );

      // 更新位置
      position = offsetPosition;
    }

    // 获取对应类型的颜色，如果没有则使用默认颜色
    const iconColor = Cesium.Color.fromCssColorString(
      MarkerTypeColorMap[marker.type] || DEFAULT_MARKER_COLOR,
    );

    // 获取标记点类型对应的图标
    // 使用动态导入会有类型问题，这里使用类型断言
    // const markerImage = new URL(`../../../assets/images/point/${marker.type}.png`, import.meta.url).href;

    // 获取显示层级范围
    const displayLevel = marker.displayLevel || { min: 0, max: 3000000 };

    // 创建距离显示条件
    const distanceDisplayCondition = new Cesium.DistanceDisplayCondition(
      displayLevel.min,
      displayLevel.max,
    );

    viewer.entities.add({
      id: newPoint.id,
      name: marker.name,
      position: position,
      show: false, // 设置初始可见性为 false
      billboard: {
        image: pointImage,
        width: 32,
        height: 32,
        scale: 1.0,
        // color: iconColor,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        distanceDisplayCondition: distanceDisplayCondition,
        disableDepthTestDistance: 10000,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      label: {
        text: marker.name,
        font: "12px sans-serif",
        fillColor: Cesium.Color.WHITE,
        backgroundColor: iconColor.withAlpha(0.7),
        showBackground: true,
        style: Cesium.LabelStyle.FILL,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -10),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        distanceDisplayCondition: distanceDisplayCondition,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      properties: {
        position,
        type: marker.type,
        isBillboard: true,
        isRightMenu: true,
        isEdit: true,
        name: marker.name,
        description: marker.description,
      },
    });
  });
};

onMounted(async () => {
  const viewerMap = await initMap(viewerDivRef);
  window.viewer = viewerMap;
  isViewer.value = true;
  tool.leftDoubleClickSetting(); // 默认关闭鼠标左键双击追踪事件
  commonStore.setViewer(viewerMap);
  initCesiumNavigation(viewerMap);
  mouseEvent.mouseMove();
  // mouseEvent.clickEntity();
  // await initCase(viewerMap);

  // 监听显示标记点列表事件
  bus.on("showMarkPointList", () => {
    if (markPointListRef.value) {
      markPointListRef.value.open();
    }
  });

  // 监听显示区域列表事件
  bus.on("showRegionList", () => {
    if (regionListRef.value) {
      regionListRef.value.open();
    }
  });

  // 监听显示区域详情事件
  bus.on("showRegionInfo", (data) => {
    if (regionInfoRef.value) {
      regionInfoRef.value.open(data.region, data.dataSource, data.entity);
    }
  });

  // 添加点击标记点事件监听
  viewerMap.screenSpaceEventHandler.setInputAction((movement: any) => {
    const pickedFeature = viewerMap.scene.pick(movement.position);
    if (Cesium.defined(pickedFeature) && pickedFeature.id) {
      const entity = pickedFeature.id;
      // 检查是否是标记点
      if (
        entity.properties &&
        entity.properties.isBillboard &&
        entity.properties.isBillboard._value === true
      ) {
        // 显示标记点列表弹窗，并传递标记点ID
        if (markPointListRef.value) {
          const pointId = entity.id;
          markPointListRef.value.open(pointId);
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 初始化预设的北京地区标记点
  initBeijingMarkers();
});
onBeforeUnmount(() => {
  bus.all.clear();
});
</script>

<style lang="scss" scoped>
@use "@/scss/app.scss";

.pointPopup {
  top: 30px;
  left: 10px;
  position: fixed;
  display: grid;
  grid-template-rows: 1fr;
  max-height: calc(100% - 50px);
  z-index: 999;
}

#cesium-viewer,
.mainContainer {
  width: 100%;
  height: 100%;
}

.layer_tool {
  position: fixed;
  top: 8vh;
  right: 10px;
}

button {
  margin: 0 15px;
  background-color: rgba(255, 255, 255, 0.7);
}

.trackPopup {
  top: 10px;
  left: 10px;
  position: fixed;
}

.mouseInfo {
  position: fixed;
  bottom: 0px;
  right: 94px;
  height: 21px;
  min-width: 560px;
  background: #0006;
}

:deep(.compass) {
  top: -16px;
  right: -16px;
  scale: 0.65;

  .compass-outer-ring,
  .compass-gyro {
    // fill: rgb(255 255 255);
    cursor: pointer;
    stroke-width: 2px;
    stroke: #fff;
  }
}

:deep(.distance-legend) {
  scale: 0.7;
  font-weight: bold;
  border-radius: 0;
  bottom: -4px !important;
  right: -20px !important;
  background: rgba(0, 0, 0, 0.4);

  .distance-legend-scale-bar {
    border-left: 2px solid #ffffff;
    border-right: 2px solid #ffffff;
    border-bottom: 2px solid #ffffff;
  }
}
</style>
