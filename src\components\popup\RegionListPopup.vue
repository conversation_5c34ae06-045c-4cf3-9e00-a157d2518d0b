<template>
  <el-dialog
    v-model="isShow"
    title="区域列表"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :before-close="close"
    class="region-list-popup"
    :append-to-body="true"
  >
    <div class="search-container">
      <!-- <el-input
        v-model="searchText"
        placeholder="搜索区域名称"
        clearable
        @clear="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch" type="primary">搜索</el-button>
        </template>
      </el-input> -->
      <el-switch
        v-model="showAllRegions"
        active-text="显示所有区域"
        inactive-text="隐藏所有区域"
        @change="toggleAllRegions"
        style="margin-top: 10px;"
      />
    </div>

    <el-table
      :data="filteredRegions"
      style="width: 100%"
      height="400"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="index" width="60" label="序号" />
      <el-table-column prop="name" label="名称" align="right"/>
      <!-- <el-table-column prop="level" label="级别" width="90" /> -->
      <!-- <el-table-column label="操作" width="150" >
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="showRegionInfo(scope.row)"
            title="查看详情"
          >
            详情
          </el-button>
          <el-button
            :type="scope.row.visible ? 'success' : 'info'"
            size="small"
            @click="toggleRegionVisibility(scope.row)"
            :title="scope.row.visible ? '隐藏区域' : '显示区域'"
          >
            {{ scope.row.visible ? '隐藏' : '显示' }}
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="close">关闭</el-button> -->
        <!-- <el-button type="primary" @click="importRegionData">导入区域数据</el-button> -->
        <el-button type="primary" plain @click="loadSampleData">加载示例数据</el-button>
        <!-- <el-button type="warning" @click="loadJiashanData">加载嘉善县数据</el-button> -->
      </span>
    </template>
  </el-dialog>

  <!-- 文件上传隐藏输入框 -->
  <input
    type="file"
    ref="fileInput"
    style="display: none"
    accept=".json,.geojson"
    @change="handleFileUpload"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import * as Cesium from 'cesium';
import { ElMessage } from 'element-plus';
import GeoJsonLoader from '@/utils/geoJsonLoader';
import bus from '~/src/utils/bus';
import beijingDistricts from '@/data/beijingDistricts.json';

// 区域信息接口
interface RegionInfo {
  id: string;
  name: string;
  level: string;
  code?: string;
  population?: number;
  area?: number;
  visible: boolean;
  dataSource: Cesium.DataSource;
  [key: string]: any;
}

// 状态
const isShow = ref(false);
const regions = ref<RegionInfo[]>([]);
const searchText = ref('');
const showAllRegions = ref(true);
const fileInput = ref<HTMLInputElement | null>(null);
const filteredRegions = ref<RegionInfo[]>([]);

/**
 * 打开弹窗
 */
const open = () => {
  isShow.value = true;
  // 初始化显示所有区域
  filteredRegions.value = [...regions.value];
};

/**
 * 关闭弹窗
 */
const close = () => {
  isShow.value = false;
};

/**
 * 处理搜索
 */
const handleSearch = () => {
  if (!searchText.value.trim()) {
    // 如果搜索文本为空，显示所有区域
    filteredRegions.value = [...regions.value];
    return;
  }

  // 执行搜索过滤
  const keyword = searchText.value.toLowerCase().trim();
  filteredRegions.value = regions.value.filter(region =>
    region.name.toLowerCase().includes(keyword) ||
    region.id.toLowerCase().includes(keyword) ||
    (region.code && region.code.toLowerCase().includes(keyword))
  );

  // 显示搜索结果数量
  ElMessage.info(`找到 ${filteredRegions.value.length} 个匹配的区域`);
};

/**
 * 显示区域详细信息
 */
const showRegionInfo = (region: RegionInfo) => {
  // 查找对应的实体
  let entity = null;
  if (region.dataSource) {
    const entities = region.dataSource.entities.values;
    for (let i = 0; i < entities.length; i++) {
      const e = entities[i];
      if (e.name === region.name || e.id === region.id) {
        entity = e;
        // 高亮显示区域
        GeoJsonLoader.highlightRegion(entity);
        break;
      }
    }
  }

  // 发送事件，显示区域详情弹窗，并传递实体对象
  bus.emit('showRegionInfo', { region, dataSource: region.dataSource, entity });
};

/**
 * 切换区域可见性
 */
const toggleRegionVisibility = (region: RegionInfo) => {
  region.visible = !region.visible;
  region.dataSource.show = region.visible;
};

/**
 * 切换所有区域可见性
 */
const toggleAllRegions = () => {
  regions.value.forEach(region => {
    region.visible = showAllRegions.value;
    region.dataSource.show = showAllRegions.value;
  });
};

/**
 * 导入区域数据
 */
const importRegionData = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

/**
 * 处理文件上传
 */
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const file = input.files[0];
  try {
    // 加载GeoJSON数据
    const geoJson = await GeoJsonLoader.loadGeoJsonFromFile(file);

    // 提取属性信息
    const properties = GeoJsonLoader.extractPropertiesFromGeoJson(geoJson);

    // 添加到Cesium查看器
    const dataSource = await GeoJsonLoader.addGeoJsonToViewer(window.viewer, geoJson, {
      outlineColor: Cesium.Color.RED, // 使用红色边界
      outlineWidth: 3, // 边界宽度
      alpha: 0, // 不显示填充色
      name: file.name
    });

    // 添加到区域列表
    properties.forEach(prop => {
      regions.value.push({
        ...prop,
        visible: true,
        dataSource
      });
    });

    // 更新过滤后的区域列表
    filteredRegions.value = [...regions.value];

    // 为区域添加悬浮和点击事件
    GeoJsonLoader.addRegionEvents(window.viewer, dataSource, {
      onClick: (entity) => {
        // 高亮显示点击的区域
        GeoJsonLoader.highlightRegion(entity);

        // 查找对应的区域信息
        const regionInfo = regions.value.find(region =>
          region.dataSource === dataSource &&
          (entity.name === region.name || entity.id === region.id)
        );

        if (regionInfo) {
          // 显示区域详情，并传递实体对象
          bus.emit('showRegionInfo', { region: regionInfo, dataSource, entity });
        }
      }
    });

    // 飞行到区域
    GeoJsonLoader.flyToGeoJson(window.viewer, dataSource);

    ElMessage.success(`成功导入 ${properties.length} 个区域`);
  } catch (error) {
    console.error('导入区域数据失败:', error);
    ElMessage.error('导入区域数据失败，请检查文件格式');
  }

  // 重置文件输入框
  if (input) {
    input.value = '';
  }
};

/**
 * 加载示例数据
 */
const loadSampleData = async () => {
  try {
    // 添加到Cesium查看器
    const dataSource = await GeoJsonLoader.addGeoJsonToViewer(window.viewer, beijingDistricts, {
      outlineColor: Cesium.Color.RED, // 使用红色边界
      outlineWidth: 3, // 边界宽度
      alpha: 0, // 不显示填充色
      name: "北京市区县"
    });

    // 提取属性信息
    const properties = GeoJsonLoader.extractPropertiesFromGeoJson(beijingDistricts);

    // 添加到区域列表
    properties.forEach(prop => {
      regions.value.push({
        ...prop,
        visible: true,
        dataSource
      });
    });

    // 更新过滤后的区域列表
    filteredRegions.value = [...regions.value];

    // 为区域添加悬浮和点击事件
    GeoJsonLoader.addRegionEvents(window.viewer, dataSource, {
      onClick: (entity) => {
        // 高亮显示点击的区域
        GeoJsonLoader.highlightRegion(entity);

        // 查找对应的区域信息
        const regionInfo = regions.value.find(region =>
          region.dataSource === dataSource &&
          (entity.name === region.name || entity.id === region.id)
        );

        if (regionInfo) {
          // 显示区域详情，并传递实体对象
          bus.emit('showRegionInfo', { region: regionInfo, dataSource, entity });
        }
      }
    });

    // 飞行到区域
    GeoJsonLoader.flyToGeoJson(window.viewer, dataSource);

    ElMessage.success(`成功加载 ${properties.length} 个北京市区县数据`);
  } catch (error) {
    console.error('加载示例数据失败:', error);
    ElMessage.error('加载示例数据失败');
  }
};

/**
 * 加载嘉善县数据
 */
const loadJiashanData = async () => {
  try {
    const jiashanUrl = "https://geo.datav.aliyun.com/areas_v3/bound/330421.json";

    // 加载GeoJSON数据
    const geoJson = await GeoJsonLoader.loadGeoJson(jiashanUrl);

    // 添加到Cesium查看器
    const dataSource = await GeoJsonLoader.addGeoJsonToViewer(window.viewer, geoJson, {
      outlineColor: Cesium.Color.RED, // 使用红色边界
      outlineWidth: 3, // 边界宽度
      alpha: 0, // 不显示填充色
      name: "嘉善县"
    });

    // 提取属性信息
    const properties = GeoJsonLoader.extractPropertiesFromGeoJson(geoJson);

    // 添加到区域列表
    properties.forEach(prop => {
      regions.value.push({
        ...prop,
        visible: true,
        dataSource
      });
    });

    // 更新过滤后的区域列表
    filteredRegions.value = [...regions.value];

    // 为区域添加悬浮和点击事件
    GeoJsonLoader.addRegionEvents(window.viewer, dataSource, {
      onClick: (entity) => {
        // 高亮显示点击的区域
        GeoJsonLoader.highlightRegion(entity);

        // 查找对应的区域信息
        const regionInfo = regions.value.find(region =>
          region.dataSource === dataSource &&
          (entity.name === region.name || entity.id === region.id)
        );

        if (regionInfo) {
          // 显示区域详情，并传递实体对象
          bus.emit('showRegionInfo', { region: regionInfo, dataSource, entity });
        }
      }
    });

    // 飞行到区域
    GeoJsonLoader.flyToGeoJson(window.viewer, dataSource);

    ElMessage.success(`成功加载嘉善县数据`);
  } catch (error) {
    console.error('加载嘉善县数据失败:', error);
    ElMessage.error('加载嘉善县数据失败，请检查网络连接');
  }
};

// 暴露方法
defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.region-list-popup {
  position: fixed;
  left: 20px;
  bottom: 20px;
}

.search-container {
  margin-bottom: 15px;
  display: flex;
  justify-content: end;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}
</style>
