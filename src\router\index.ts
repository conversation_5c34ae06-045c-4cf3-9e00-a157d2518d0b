import { RouteRecordRaw, createRouter, createWebHistory } from "vue-router";

export const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/map/map.vue"),
  },
  {
    path: "/app",
    name: "App",
    component: () => import("@/views/app/index.vue"),
  },
  {
    path: "/swipe",
    name: "swipe",
    component: () => import("@/widgets/Swipe/index.vue"),
  },
  {
    path: "/plan",
    name: "plan",
    component: () => import("@/widgets/AircraftViewer/AircraftViewer.vue"),
  },
  {
    path: "/daynight",
    name: "daynight",
    component: () => import("@/widgets/DayNightViewer/index.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
