<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { createModel } from "@/utils/createModel";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const planURL = "/src/assets/models/plan1.glb";

let aircraftEntity: Cesium.Entity | null = null;
let animationInterval: number | null = null;

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();

  const startLon = 116.4074;
  const startLat = 39.9042;
  const endLon = 116.5074;
  const endLat = 39.9042;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  const heights = [100, 800, 1500, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  for (let i = 0; i < pointCount; i++) {
    const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
    const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
    const height = heights[i];

    const time = Cesium.JulianDate.addSeconds(
      start,
      i * secondsBetweenPoints,
      new Cesium.JulianDate(),
    );
    const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    position.addSample(time, positionSample);
  }

  const stop = Cesium.JulianDate.addSeconds(
    start,
    (pointCount - 1) * secondsBetweenPoints,
    new Cesium.JulianDate(),
  );

  aircraftEntity = viewer.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 64,
      maximumScale: 200,
      scale: 1,
    },
  });

  viewer.clock.startTime = start.clone();
  viewer.clock.stopTime = stop.clone();
  viewer.clock.currentTime = start.clone();
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;

  viewer.trackedEntity = aircraftEntity;
  // 替换原来的 viewer.trackedEntity = aircraftEntity;

  // viewer.trackedEntity = undefined; // 不使用默认跟踪，使用自定义视角

  // const updateCamera = () => {
  //   if (!aircraftEntity || !viewer) return;
  //   const position = aircraftEntity.position?.getValue(
  //     viewer.clock.currentTime,
  //   );
  //   const orientation = aircraftEntity.orientation?.getValue(
  //     viewer.clock.currentTime,
  //   );

  //   if (position && orientation) {
  //     const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
  //       position,
  //       Cesium.HeadingPitchRoll.fromQuaternion(orientation),
  //     );

  //     viewer.scene.camera.lookAtTransform(
  //       transform,
  //       new Cesium.Cartesian3(0, 20, 10),
  //     );
  //   }
  // };

  // viewer.clock.onTick.addEventListener(updateCamera);

  // 初始相机视角（可选）
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
  //   orientation: {
  //     heading: Cesium.Math.toRadians(90),
  //     pitch: Cesium.Math.toRadians(-45),
  //     roll: 0.0,
  //   },
  // });
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped></style>
