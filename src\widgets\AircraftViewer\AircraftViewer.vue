<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <!-- 视角切换控制面板 -->
    <div class="camera-controls">
      <button
        @click="toggleCameraMode"
        :class="['camera-btn', { active: isFirstPersonView }]"
      >
        {{ isFirstPersonView ? "第一视角" : "第三视角" }}
      </button>
      <button @click="resetAnimation" class="reset-btn">重置动画</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const planURL = "/src/assets/models/plan1.glb";

let aircraftEntity: Cesium.Entity | null = null;
let animationInterval: number | null = null;

// 视角控制相关变量
const isFirstPersonView = ref(false);
let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();

  const startLon = 116.4074;
  const startLat = 39.9042;
  const endLon = 116.5074;
  const endLat = 39.9042;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  const heights = [100, 800, 1500, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  for (let i = 0; i < pointCount; i++) {
    const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
    const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
    const height = heights[i];

    const time = Cesium.JulianDate.addSeconds(
      start,
      i * secondsBetweenPoints,
      new Cesium.JulianDate(),
    );
    const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    position.addSample(time, positionSample);
  }

  const stop = Cesium.JulianDate.addSeconds(
    start,
    (pointCount - 1) * secondsBetweenPoints,
    new Cesium.JulianDate(),
  );

  aircraftEntity = viewer.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 64,
      maximumScale: 200,
      scale: 1,
    },
  });

  viewer.clock.startTime = start.clone();
  viewer.clock.stopTime = stop.clone();
  viewer.clock.currentTime = start.clone();
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;

  // 默认使用第三视角（跟踪模式）
  viewer.trackedEntity = aircraftEntity;

  // 设置初始相机视角
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
    orientation: {
      heading: Cesium.Math.toRadians(90),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0,
    },
  });
};

// 第一视角相机更新函数
const updateFirstPersonCamera = () => {
  if (!aircraftEntity || !viewer) return;

  const position = aircraftEntity.position?.getValue(viewer.clock.currentTime);
  const orientation = aircraftEntity.orientation?.getValue(
    viewer.clock.currentTime,
  );

  if (position && orientation) {
    // 创建飞机的本地坐标系
    const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
      position,
      Cesium.HeadingPitchRoll.fromQuaternion(orientation),
    );

    // 设置相机位置为驾驶舱视角（飞机前方稍微偏上的位置）
    const cameraOffset = new Cesium.Cartesian3(0, -2, 1); // 前方2米，上方1米

    viewer.scene.camera.lookAtTransform(transform, cameraOffset);
  }
};

// 切换视角模式
const toggleCameraMode = () => {
  if (!viewer || !aircraftEntity) return;

  isFirstPersonView.value = !isFirstPersonView.value;

  if (isFirstPersonView.value) {
    // 切换到第一视角
    viewer.trackedEntity = undefined;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 添加第一视角相机更新监听器
    cameraUpdateListener = viewer.clock.onTick.addEventListener(
      updateFirstPersonCamera,
    );
  } else {
    // 切换到第三视角
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 恢复跟踪模式
    viewer.trackedEntity = aircraftEntity;
  }
};

// 重置动画
const resetAnimation = () => {
  if (!viewer) return;

  viewer.clock.currentTime = viewer.clock.startTime.clone();
  viewer.clock.shouldAnimate = true;

  // 如果是第一视角，重新设置相机
  if (isFirstPersonView.value) {
    updateFirstPersonCamera();
  }
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  if (cameraUpdateListener) {
    cameraUpdateListener();
  }
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.camera-btn,
.reset-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(42, 42, 42, 0.8);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-btn:hover,
.reset-btn:hover {
  background: rgba(42, 42, 42, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.camera-btn.active {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 0.3);
}

.camera-btn.active:hover {
  background: rgba(0, 122, 255, 0.9);
}
</style>
