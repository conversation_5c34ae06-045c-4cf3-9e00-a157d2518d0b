/* @tailwind base; */
@tailwind components;
@tailwind utilities;

:root {
  /* font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif; */
  line-height: 1.5;
  font-weight: 400;

  /* color-scheme: light dark; */
  color: rgba(255, 255, 255, 0.87);
  /* background-color: #242424; */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  /* display: flex;
  place-items: center; */
  min-width: 320px;
  max-height: 100vh;
}

#app {
  position: relative;
  height: 100vh;
  width: 100%;
  /* text-align: center; */
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
} */

html,
body {
  padding: 0;
  margin: 0;
}

.navigation-controls {
  display: none;
  /* z-index: 10;
  border: none !important;
  width: 40px !important;
  .navigation-control-icon-zoom-in{
    color: black;
  }
  .navigation-control-icon-reset{
    fill: black;
  }
  .navigation-control-icon-zoom-out{
    color: black;
  } */
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 3px;
  height: 9px;
  background: transparent;
  border-left: 1px solid transparent;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0);
  border-radius: 5px;
  background-color: transparent;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-width: 1px;
  border-style: solid;
  border: none;
  background: rgba(35, 169, 238, 0.7);
  border-radius: 5px;
}

.templateDiv {
  width: 100%;
  height: 100%;
}