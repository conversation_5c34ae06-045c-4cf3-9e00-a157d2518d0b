{"name": "cesium_ts", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@rollup/plugin-commonjs": "^25.0.7", "axios": "^1.4.0", "cesium": "1.118", "cesium-navigation-es6": "^3.0.8", "echarts": "^5.6.0", "element-plus": "^2.9.0", "mitt": "^3.0.1", "obj2gltf": "^3.1.6", "pinia": "^3.0.0", "sass": "^1.83.0", "sass-loader": "^13.3.2", "tailwindcss": "^3.4.17", "uuid": "^11.0.2", "vue": "^3.5.14", "vue-router": "^4.2.4"}, "devDependencies": {"@iconify-json/ep": "^1.1.13", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "^3.5.3", "typescript": "^5.8.3", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.18.1", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vite-plugin-cesium": "^1.2.22", "vite-plugin-require-transform": "^1.0.21", "vue-tsc": "^1.8.5"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}