<template>
  <div class="min-h-screen bg-gray-100 p-6">
    <div>
      <router-link to="/"> HOME </router-link>
    </div>
    <div class="max-w-4xl mx-auto grid gap-4" v-if="list.length > 0">
      <div
        v-for="item in list"
        :key="item.path"
        class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
      >
        <router-link
          :to="item.path"
          class="block p-4 text-gray-800 hover:text-blue-600 font-medium"
        >
          {{ item.name }}
        </router-link>
      </div>
    </div>
    <el-empty v-else description="" />
  </div>
</template>

<script lang="ts" setup>
import { routes } from "@/router";

const list = routes
  .filter((route) => !["/", "/app"].includes(route.path))
  .map((route) => {
    return {
      path: route.path,
      name: route.name,
    };
  });
</script>
