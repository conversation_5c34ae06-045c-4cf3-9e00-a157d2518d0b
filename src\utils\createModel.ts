import * as Cesium from "cesium";

/**
 * 在 Cesium 场景中添加一个 3D 模型实体
 * @param viewer Cesium.Viewer 实例
 * @param url 模型的资源地址
 * @param height 模型的高度（海拔）
 * @param lon 模型的经度，默认为 116.4074（北京）
 * @param lat 模型的纬度，默认为 39.9042（北京）
 * @param heading 模型的朝向（绕Z轴旋转），单位为角度
 * @param pitch 模型的俯仰角（绕X轴旋转），单位为角度
 * @param roll 模型的滚转角（绕Y轴旋转），单位为角度
 */
export function createModel(
  viewer: Cesium.Viewer,
  url: string,
  height: number,
  lon: number = 116.4074,
  lat: number = 39.9042,
  headingDeg: number = 0,
  pitchDeg: number = 0,
  rollDeg: number = 0,
) {
  if (!viewer) return;
  // viewer.entities.removeAll();

  const position = Cesium.Cartesian3.fromDegrees(lon, lat, height);

  const heading = Cesium.Math.toRadians(headingDeg);
  const pitch = Cesium.Math.toRadians(pitchDeg);
  const roll = Cesium.Math.toRadians(rollDeg);

  const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
  const orientation = Cesium.Transforms.headingPitchRollQuaternion(
    position,
    hpr,
  );

  const entity = viewer.entities.add({
    name: url,
    position,
    orientation,
    model: {
      uri: url,
      minimumPixelSize: 128,
      maximumScale: 20000,
      scale: 2,
    },
  });
  viewer.trackedEntity = entity;
  return entity;
}
