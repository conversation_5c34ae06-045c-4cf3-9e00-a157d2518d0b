<!-- 夜景灯光变幻 -->
<script setup lang="ts">
import * as Cesium from "cesium";

import { onMounted, onUnmounted, ref } from "vue";

import { initMap } from "@/utils/initMap";

import MapCoating from "@/views/map/components/map-coating.vue";

let viewer: Cesium.Viewer;

const isNightLightsEnabled = ref(false);
let nightLightsImageryLayer: Cesium.ImageryLayer | undefined = undefined;

const toggleNightLights = async () => {
  if (!viewer) return;
  isNightLightsEnabled.value = !isNightLightsEnabled.value;

  viewer.scene.globe.enableLighting = isNightLightsEnabled.value;
  viewer.clock.shouldAnimate = isNightLightsEnabled.value;

  //   if (isNightLightsEnabled.value) {
  //     // Add the default world imagery with night lights
  //     nightLightsImageryLayer = await viewer.imageryLayers.addImageryProvider(
  //       await Cesium.createWorldImageryAsync({
  //         // You can specify options here if needed
  //       }),
  //     );
  //   } else {
  //     // Remove the imagery layer
  //     if (nightLightsImageryLayer) {
  //       viewer.imageryLayers.remove(nightLightsImageryLayer);
  //       nightLightsImageryLayer = undefined;
  //     }
  //   }
};
const viewerDivRef = ref<HTMLDivElement>();

onMounted(async () => {
  if (viewer) return;
  viewer = await initMap(viewerDivRef);
  // Enable time progression deand animation
  viewer.clock.shouldAnimate = true;
  viewer.clock.currentTime = Cesium.JulianDate.now();
  // Speed up time (e.g., 1 hour per second)
  viewer.clock.multiplier = 3600;

  // Set initial view to show the entire Earth
  viewer.camera.flyHome(0);
  toggleNightLights();
});

// Optional: Clean up the imagery layer when the component is unmounted
// onUnmounted(() => {
//   if (window.viewer && nightLightsImageryLayer) {
//     (window.viewer as Cesium.Viewer).imageryLayers.remove(
//       nightLightsImageryLayer,
//     );
//     nightLightsImageryLayer = undefined;
//   }
// });

// Initial setup on mounted if needed, although the toggle handles the state
// onMounted(() => {
//   if (window.viewer) {
//     // You could set an initial state here if desired
//   }
// });
</script>

<template>
  <div class="day-night-viewer mainContainer">
    <div id="cesium-viewer" ref="viewerDivRef"></div>
    <div class="tool fixed bottom-2 left-2 rounded">
      <el-button
        @click="toggleNightLights"
        class="relative cursor-pointer p-4 font-semibold text-cyan-300 tracking-wide uppercase border border-cyan-400 rounded-lg bg-black/50 shadow-lg hover:bg-cyan-500 hover:text-black transition-all duration-300 ease-in-out before:absolute before:inset-0 before:rounded-md before:blur-sm before:opacity-20 before:bg-cyan-400 hover:before:opacity-40"
      >
        {{ isNightLightsEnabled ? "关闭夜景灯光" : "开启夜景灯光" }}
      </el-button>
    </div>
    <!-- <MapCoating class="mouseInfo" /> -->
  </div>
</template>

<style scoped>
/* Add any specific styles here if needed, but prefer Tailwind utilities */
</style>
