<template>
  <div class="toolBox text-center">
    <div class="boxBg">
      <el-tooltip :content="is3D ? ' 切换二维' : '切换三维'" placement="left">
        <i
          class="iconfont font24 color999 gis-a-3d"
          @click="changSceneMode"
          v-if="is3D"
        ></i>
        <i
          class="iconfont font24 color999 gis-erwei"
          @click="changSceneMode"
          v-if="!is3D"
        ></i>
      </el-tooltip>
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        trigger="click"
      >
        <template #reference>
          <div>
            <i class="iconfont font24 color999 gis-tuceng"></i>
          </div>
        </template>
        <div class="popMain display-flex">
          <div
            class="layer display-flex"
            :class="{ active_card: layer_c }"
            @click="changeLayer('img')"
          >
            <img
              src="@/assets/images/map/img_c.png"
              width="100"
              height="80"
              style="cursor: pointer"
            />
            <span>影像</span>
          </div>
          <div
            class="layer display-flex"
            :class="{ active_card: !layer_c && !!layerType }"
            @click="changeLayer('vec')"
          >
            <img
              src="@/assets/images/map/vec_c.png"
              width="100"
              height="80"
              style="cursor: pointer"
            />
            <span>矢量</span>
          </div>
        </div>
      </el-popover>
      <el-tooltip content="重置视角" placement="left">
        <i class="iconfont font24 color999 gis-chushi" @click="tool.reset"></i>
      </el-tooltip>
      <el-tooltip content="放大" placement="left">
        <i
          class="iconfont font24 color999 gis-fangda"
          @click="tool.zoom('in')"
        ></i>
      </el-tooltip>
      <el-tooltip content="缩小" placement="left">
        <i
          class="iconfont font24 color999 gis-suoxiao"
          @click="tool.zoom('out')"
        ></i>
      </el-tooltip>
      <el-tooltip content="定位" placement="left">
        <i
          class="iconfont font24 color999 gis-dingwei"
          @click="getCurrentPosition"
        ></i>
      </el-tooltip>
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        :hide="hideMeasure"
        trigger="click"
      >
        <template #reference>
          <div>
            <el-tooltip content="测量工具" placement="left">
              <i class="iconfont font24 color999 gis-measure-tool"></i>
            </el-tooltip>
          </div>
        </template>
        <div class="popMain">
          <div class="popover-title">
            <span>测量工具</span>
            <!-- <span class="float-right red cursor-pointer">
              <i class="iconfont gis-shanchu"></i>
            </span> -->
          </div>
          <div class="popContent">
            <div
              class="iconBtn distance"
              @click="tool.drawLineMeasureGraphics()"
            >
              <i class="iconfont gis-shuipingceju"></i>
              <span class="font12 text-center mt8 color666">测距离</span>
            </div>
            <div
              class="iconBtn area"
              @click="tool.drawAreaMeasureGraphics('Polygon')"
            >
              <i class="iconfont gis-mianji"></i>
              <span class="font12 text-center mt8 color666">测面积</span>
            </div>
            <div class="iconBtn area" @click="tool.azimuthAngle()">
              <i class="iconfont gis-fangweijiao"></i>
              <span class="font12 text-center mt8 color666">方位角</span>
            </div>
          </div>
        </div>
      </el-popover>
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        trigger="click"
      >
        <template #reference>
          <div>
            <el-tooltip content="兵力部署" placement="left">
              <i class="iconfont font24 color999 gis-shiliang"></i>
            </el-tooltip>
          </div>
        </template>
        <div class="popMain">
          <div class="popover-title">
            <span>兵力部署</span>
          </div>
          <div class="popContent">
            <div class="iconBtn distance" @click="MarkModel.addModel('target')">
              <i class="iconfont gis-icon_mubiao"></i>
              <span class="font12 text-center mt8 color666">防护目标</span>
            </div>
            <div class="iconBtn area" @click="MarkModel.addModel('tank1')">
              <i class="iconfont gis-tanke"></i>
              <span class="font12 text-center mt8 color666">防护坦克</span>
            </div>
            <div
              class="iconBtn area"
              @click="MarkModel.addModel('TransportVehicle1')"
            >
              <i class="iconfont gis-daodanzhuangjiache"></i>
              <span class="font12 text-center mt8 color666">反导装甲</span>
            </div>
            <div
              class="iconBtn distance"
              @click="MarkModel.addModel('warship')"
            >
              <i class="iconfont gis-junjian"></i>
              <span class="font12 text-center mt8 color666">军舰</span>
            </div>
            <div class="iconBtn area" @click="MarkModel.addModel('warplane')">
              <i class="iconfont gis-zhandouji"></i>
              <span class="font12 text-center mt8 color666">无人机</span>
            </div>
            <div class="iconBtn area" @click="MarkModel.addModel('tank')">
              <i class="iconfont gis-xiangxingfuhao-35-tanke"></i>
              <span class="font12 text-center mt8 color666">机动-坦克</span>
            </div>
            <div
              class="iconBtn area"
              @click="MarkModel.addModel('TransportVehicle')"
            >
              <i class="iconfont gis-lunshizhuangjiache"></i>
              <span class="font12 text-center mt8 color666">机动-装甲</span>
            </div>
          </div>
        </div>
      </el-popover>
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        trigger="click"
      >
        <template #reference>
          <div>
            <el-tooltip content="标绘" placement="left">
              <i class="iconfont font24 color999 gis-biaoji"></i>
            </el-tooltip>
          </div>
        </template>
        <div class="popMain">
          <div class="popover-title">
            <span>标记工具</span>
          </div>
          <div class="popContent">
            <div class="iconBtn distance" @click="markTool.markPoint()">
              <i class="iconfont gis-dingwei1"></i>
              <span class="font12 text-center mt8 color666">标记点</span>
            </div>
            <div class="iconBtn area" @click="markTool.markPolyline()">
              <i class="iconfont gis-xian"></i>
              <span class="font12 text-center mt8 color666">贴地线</span>
            </div>
            <div class="iconBtn area" @click="markTool.markPlanePolygon()">
              <i class="iconfont gis-quyu"></i>
              <span class="font12 text-center mt8 color666">贴地面</span>
            </div>
            <div class="iconBtn area" @click="markTool.markPlaneCircle()">
              <i class="iconfont gis-yuan"></i>
              <span class="font12 text-center mt8 color666">贴地圆形</span>
            </div>
            <div class="iconBtn area" @click="showRegions()">
              <i class="iconfont gis-quyu"></i>
              <span class="font12 text-center mt8 color666">区域管理</span>
            </div>
          </div>
        </div>
      </el-popover>
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        trigger="click"
      >
        <template #reference>
          <div>
            <el-tooltip content="态势/分析/图表" placement="left">
              <i class="iconfont font24 color999 gis-ditutaishi"></i>
            </el-tooltip>
          </div>
        </template>
        <div class="popMain mb15">
          <div class="popover-title">
            <span>态势场景演示</span>
          </div>
          <div class="popContent1 ml5">
            <div class="row cursor-pointer" @click="player('yanshi')">
              1. 默认态势场景(海、陆、空)
            </div>
            <div class="row cursor-pointer" @click="player('sport')">
              2. 围点打圆(陆)
            </div>
            <div class="row cursor-pointer" @click="showFlags">3. 标记点</div>
          </div>
          <div class="popContent1 ml5" v-for="(item, i) in store">
            <div class="row cursor-pointer" @click="initDemo(item.id)">
              {{ i + 3 + ". " + item.name }} 场景
            </div>
          </div>
        </div>
      </el-popover>
      <!-- <el-popover popper-class="layer_pop" width=230 placement="left" trigger="click">
        <template #reference>
          <div>
            <el-tooltip content="防空演示" placement="left">
              <i class="iconfont font24 color999 gis-zhongxinkaishi" @click="player('airDef')"></i>
            </el-tooltip>
          </div>
        </template>
      </el-popover> -->
      <el-popover
        popper-class="layer_pop"
        width="230"
        placement="left"
        trigger="click"
      >
        <template #reference>
          <div>
            <el-tooltip content="陆空对抗" placement="left">
              <i
                class="iconfont font24 color999 gis-zhandouji"
                @click="player('landAirCombat')"
              ></i>
            </el-tooltip>
          </div>
        </template>
      </el-popover>
    </div>
  </div>
</template>
<script lang="ts" setup>
import * as Cesium from "cesium";

import "@/assets/iconfont/iconfont.css";
import "@/assets/iconfont/iconfont.js";

import pointHadler from "@/components/point/pointHadler.ts";

import MarkModel from "../yanshi/markModel.ts";
import markTool from "./markTool";
import tool from "./tool.ts";
import { ElMessage } from "element-plus";
//存储模型轨迹

import { createImageryLayers } from "~/src/config/map-baselayer.ts";
import { ModelPath } from "~/src/store/modelPath.ts";
import bus from "~/src/utils/bus.ts";

const store = ModelPath().demoData as any[]; //存储模型轨迹

const is3D = ref(true);
const emit = defineEmits(["initModel"]);
const { imgLayer, ciaLayer, vecLayer } = createImageryLayers();

const reload = () => {
  window.location.reload();
};
const layer_c = ref(true);
const layerType = ref();
// const markTool = new markToolAll()

const changeLayer = (param: any) => {
  const viewer = window.viewer;
  if (layerType.value === param && param === "vec") return;
  viewer.imageryLayers.remove(vecLayer, false);
  viewer.imageryLayers.remove(ciaLayer, false);
  layerType.value = param;
  if (param == "img") {
    if (layer_c.value) {
      viewer.imageryLayers.remove(vecLayer, false);
      viewer.imageryLayers.remove(ciaLayer, false);
      layer_c.value = false;
      layerType.value = null;
      return;
    }
    layer_c.value = true;
    viewer.imageryLayers.add(imgLayer);
    viewer.imageryLayers.add(ciaLayer);
  } else {
    layer_c.value = false;
    viewer.imageryLayers.add(vecLayer);
  }
};
const changSceneMode = () => {
  const scene = window.viewer.scene;
  is3D.value = !is3D.value;
  is3D.value ? scene.morphTo3D(0) : scene.morphTo2D(0);
};
// 隐藏测量工具弹窗
const hideMeasure = () => {
  console.log(2222);
};
// 获取当前位置信息
const getCurrentPosition = () => {
  let data: any = {};
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(success, error);
    function success(position: { coords: { latitude: any; longitude: any } }) {
      //position.coords (只读) 返回一个定义了当前位置的Coordinates对象。
      //position.timestamp (只读) 返回一个时间戳DOMTimeStamp， 这个时间戳表示获取到的位置的时间。
      var lat = position.coords.latitude; //当前位置的纬度
      var lon = position.coords.longitude; //当前位置精度
      data.lat = lat;
      data.lon = lon;
      data.name = "currentPosition";
      pointHadler.currentPosition(data);
      window.viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(lon, lat, 34000.0),
      });
      // console.log('当前位置的精度： ' + lon + '  当前位置的纬度： ' + lat);
    }
    function error(err: any) {
      switch (err.code) {
        case err.PERMISSION_DENIED:
          ElMessage({
            message: "用户拒绝对获取地理位置的请求。",
            type: "error",
          });
          break;
        case err.POSITION_UNAVAILABLE:
          ElMessage({ message: "位置信息是不可用的。", type: "error" });
          break;
        case err.TIMEOUT:
          ElMessage({ message: "请求用户地理位置超时。", type: "error" });
          break;
        case err.UNKNOWN_ERROR:
          ElMessage({ message: "未知错误。", type: "error" });
          break;
      }
    }
  } else {
    ElMessage({ message: "未知错误。", type: "error" });
  }
};
const player = (value: any) => {
  // 先关闭所有场景演示
  bus.emit("closeAllScenes");
  // 然后打开指定的场景
  bus.emit("initModel", { isShow: true, type: value });
};
const initDemo = (value: any) => {
  // 先关闭所有场景演示
  bus.emit("closeAllScenes");
  // 然后打开指定的场景
  bus.emit("initDemo", { id: value, isShow: true });
};

const showFlags = () => {
  // 先关闭所有场景演示
  bus.emit("closeAllScenes");
  // 触发显示标记点列表
  bus.emit("showMarkPointList");
};

const showRegions = () => {
  // 先关闭所有场景演示
  bus.emit("closeAllScenes");
  // 触发显示区域列表
  bus.emit("showRegionList");
};
</script>
<style lang="scss">
// popper弹出窗属性
.layer_pop {
  padding: 0px !important;
  border: none !important;
  background: #e9e7e7 !important;

  .el-popper__arrow::before {
    display: none !important;
  }

  .popMain {
    .layer {
      margin: 6px;
      flex-direction: column;
      color: var(--gis-default-color);
      border: 2px solid transparent;

      span {
        margin: -20px 0 0 10px;
      }
    }

    .active_card {
      border: 2px solid #3b7cff;
      border-radius: 3px;
    }

    .popover-title {
      height: 40px;
      line-height: 40px;
      width: 90%;
      margin: auto;
    }

    .popContent {
      width: 90%;
      margin: auto;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 80px;
      column-gap: 20px;
      row-gap: 10px;
      margin-bottom: 10px;

      .iconBtn {
        display: flex;
        flex-direction: column;
        cursor: pointer;

        .iconfont {
          // font-size: 26px;
          font-size: 40px;
          background: #fff;
          align-items: center;
          justify-content: center;
          display: flex;
          border-radius: 8px;
          padding: 0;
          // padding: 7px 0;
        }

        &:hover,
        &:active {
          color: #3b7cff;
        }
      }
    }
  }
}

.boxBg {
  background: #e9e7e7;
  border-radius: 8px;
  display: grid;

  .el-tooltip__trigger {
    padding: 0 !important;
  }

  .iconfont,
  .el-tooltip__trigger {
    cursor: pointer;
    border-bottom: 1px solid #999999;
    padding: 0 7px;

    &:last-child {
      border-bottom: none !important;
      border-radius: 0 0 8px 8px;
    }

    &:first-child {
      border-radius: 8px 8px 0 0;
    }

    &:hover,
    &:active {
      background: #fff;
      color: #3b7cff;
    }
  }
}
</style>
