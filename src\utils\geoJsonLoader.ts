import * as Cesium from "cesium";
import { v4 as uuid4 } from "uuid";
import tool from "~/src/components/tool/tool";

/**
 * GeoJSON数据加载和处理工具
 * 用于加载GeoJSON格式的区域数据，并转换为Cesium实体
 */
export class GeoJsonLoader {
  /**
   * 当前高亮的区域实体
   * 用于跟踪当前被高亮的实体，确保同一时间只有一个区域被高亮
   */
  private static currentHighlightedEntity: Cesium.Entity | null = null;
  /**
   * 加载GeoJSON数据
   * @param url GeoJSON数据的URL
   * @returns Promise<any> GeoJSON数据对象
   */
  static async loadGeoJson(url: string): Promise<any> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to load GeoJSON: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error loading GeoJSON:", error);
      throw error;
    }
  }

  /**
   * 从本地文件加载GeoJSON数据
   * @param file 本地GeoJSON文件
   * @returns Promise<any> GeoJSON数据对象
   */
  static loadGeoJsonFromFile(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          resolve(data);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = (e) => {
        reject(e);
      };
      reader.readAsText(file);
    });
  }

  /**
   * 将GeoJSON数据转换为Cesium实体
   * @param geoJson GeoJSON数据对象
   * @param options 转换选项
   * @returns Cesium.DataSource
   */
  static async geoJsonToCesiumEntities(
    geoJson: any,
    options: {
      fillColor?: Cesium.Color;
      outlineColor?: Cesium.Color;
      outlineWidth?: number;
      alpha?: number;
      clampToGround?: boolean;
      name?: string;
    } = {}
  ): Promise<Cesium.DataSource> {
    // 默认选项
    const defaultOptions = {
      fillColor: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.RED, // 修改默认边界颜色为红色
      outlineWidth: 3, // 增加边界宽度，使其更明显
      alpha: 0.3, // 默认不显示填充色
      clampToGround: true,
      name: "GeoJSON Data"
    };

    // 合并选项
    // const mergedOptions = { ...defaultOptions, ...options };
    const mergedOptions = defaultOptions;

    // 创建数据源
    const dataSource = new Cesium.GeoJsonDataSource(mergedOptions.name);

    // 加载GeoJSON数据
    await dataSource.load(geoJson, {
      stroke: mergedOptions.outlineColor,
      strokeWidth: mergedOptions.outlineWidth,
      fill: mergedOptions.fillColor.withAlpha(mergedOptions.alpha),
      clampToGround: mergedOptions.clampToGround
    });

    return dataSource;
  }

  /**
   * 将GeoJSON数据添加到Cesium查看器
   * @param viewer Cesium查看器
   * @param geoJson GeoJSON数据对象
   * @param options 转换选项
   * @returns Promise<Cesium.DataSource>
   */
  static async addGeoJsonToViewer(
    viewer: Cesium.Viewer,
    geoJson: any,
    options: {
      fillColor?: Cesium.Color;
      outlineColor?: Cesium.Color;
      outlineWidth?: number;
      alpha?: number;
      clampToGround?: boolean;
      name?: string;
    } = {}
  ): Promise<Cesium.DataSource> {
    const dataSource = await this.geoJsonToCesiumEntities(geoJson, options);
    await viewer.dataSources.add(dataSource);
    return dataSource;
  }

  /**
   * 飞行到GeoJSON数据的边界
   * @param viewer Cesium查看器
   * @param dataSource GeoJSON数据源
   */
  static flyToGeoJson(viewer: Cesium.Viewer, dataSource: Cesium.DataSource): void {
    // 获取数据源中的所有实体
    const entities = dataSource.entities.values;
    if (entities.length === 0) return;

    // 计算所有实体的边界矩形
    let west = 180;
    let south = 90;
    let east = -180;
    let north = -90;

    entities.forEach(entity => {
      if (entity.polygon && entity.polygon.hierarchy) {
        try {
          // 获取多边形的坐标
          const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now());
          if (hierarchy && hierarchy.positions) {
            const positions = hierarchy.positions;

            // 遍历所有坐标点，找出边界
            positions.forEach((position: Cesium.Cartesian3) => {
              const cartographic = Cesium.Cartographic.fromCartesian(position);
              const longitude = Cesium.Math.toDegrees(cartographic.longitude);
              const latitude = Cesium.Math.toDegrees(cartographic.latitude);

              west = Math.min(west, longitude);
              south = Math.min(south, latitude);
              east = Math.max(east, longitude);
              north = Math.max(north, latitude);
            });
          }
        } catch (error) {
          console.error('Error processing polygon:', error);
        }
      }
    });

    // 添加一些边距
    const padding = 0.1; // 边距（度）
    const paddedRectangle = Cesium.Rectangle.fromDegrees(
      west - padding,
      south - padding,
      east + padding,
      north + padding
    );

    // 计算区域的大小（对角线距离）
    const diagonalDistance = Cesium.Cartesian3.distance(
      Cesium.Cartesian3.fromDegrees(west, south),
      Cesium.Cartesian3.fromDegrees(east, north)
    );

    // 根据区域大小调整相机高度，避免过度放大
    // 设置最小高度为30000米，确保不会过度放大
    const minHeight = 30000;
    // 根据区域大小计算合适的高度，但不小于最小高度
    const calculatedHeight = Math.max(diagonalDistance * 0.5, minHeight);

    // 飞行到边界矩形
    viewer.camera.flyTo({
      destination: paddedRectangle,
      duration: 1.5,
      complete: () => {
        // 完成后设置相机高度和视角
        const center = Cesium.Cartesian3.fromDegrees(
          (west + east) / 2,
          (south + north) / 2,
          calculatedHeight * 0.3 // 降低相机位置，但保持视角
        );
        // 设置相机视角，确保能看到整个区域但不会过度放大
        viewer.camera.lookAt(center, new Cesium.Cartesian3(0, 0, calculatedHeight));
      }
    });
  }

  /**
   * 从GeoJSON数据中提取属性信息
   * @param geoJson GeoJSON数据对象
   * @returns 属性信息数组
   */
  static extractPropertiesFromGeoJson(geoJson: any): any[] {
    const properties: any[] = [];

    if (geoJson.features && Array.isArray(geoJson.features)) {
      geoJson.features.forEach((feature: any) => {
        if (feature.properties) {
          properties.push({
            id: feature.id || uuid4(),
            ...feature.properties
          });
        }
      });
    }

    return properties;
  }

  /**
   * 为GeoJSON数据源中的每个实体添加悬浮和点击事件
   * @param viewer Cesium查看器
   * @param dataSource GeoJSON数据源
   * @param options 事件配置选项
   */
  static addRegionEvents(
    viewer: Cesium.Viewer,
    dataSource: Cesium.DataSource,
    options: {
      onHover?: (entity: Cesium.Entity) => void;
      onClick?: (entity: Cesium.Entity) => void;
    } = {}
  ): void {
    // 获取数据源中的所有实体
    const entities = dataSource.entities.values;
    if (entities.length === 0) return;

    // 创建屏幕空间事件处理器
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

    // 添加鼠标移动事件处理
    handler.setInputAction((movement: any) => {
      const pickedFeature = viewer.scene.pick(movement.endPosition);
      if (Cesium.defined(pickedFeature) && pickedFeature.id) {
        const entity = pickedFeature.id;

        // 检查是否是来自该数据源的实体
        if (dataSource.entities.contains(entity) && entity.polygon) {
          // 显示小手光标
          tool.cesiumCursorConvert("pointer");

          // 调用自定义悬浮事件处理函数
          if (options.onHover) {
            options.onHover(entity);
          }
        } else {
          // 恢复默认光标
          tool.cesiumCursorConvert("default");
        }
      } else {
        // 恢复默认光标
        tool.cesiumCursorConvert("default");
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 添加鼠标点击事件处理
    handler.setInputAction((movement: any) => {
      const pickedFeature = viewer.scene.pick(movement.position);
      if (Cesium.defined(pickedFeature) && pickedFeature.id) {
        const entity = pickedFeature.id;

        // 检查是否是来自该数据源的实体
        if (dataSource.entities.contains(entity) && entity.polygon) {
          // 调用自定义点击事件处理函数
          if (options.onClick) {
            options.onClick(entity);
          }
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  /**
   * 高亮显示区域
   * @param entity 要高亮显示的实体
   * @param color 高亮颜色
   * @param alpha 透明度
   */
  static highlightRegion(entity: Cesium.Entity, color: Cesium.Color = Cesium.Color.BLUE, alpha: number = 0.5): void {
    if (!entity || !entity.polygon) return;

    // 如果当前实体已经是高亮状态，不需要重复操作
    if (this.currentHighlightedEntity === entity) return;

    // 如果有其他实体已经高亮，先取消其高亮状态
    if (this.currentHighlightedEntity) {
      this.unhighlightRegion(this.currentHighlightedEntity);
    }

    // 保存原始样式
    if (!entity.properties) {
      entity.properties = new Cesium.PropertyBag();
    }

    // 保存原始填充颜色和透明度
    const originalMaterial = entity.polygon.material;
    let originalAlpha = 0;

    if (entity.polygon.material instanceof Cesium.ColorMaterialProperty) {
      const colorProperty = entity.polygon.material as Cesium.ColorMaterialProperty;
      if (colorProperty.color) {
        const colorValue = colorProperty.color.getValue(Cesium.JulianDate.now());
        if (colorValue) {
          originalAlpha = colorValue.alpha;
        }
      }
    }

    entity.properties.originalMaterial = originalMaterial;
    entity.properties.originalAlpha = originalAlpha;

    // 应用高亮样式 - 使用ColorMaterialProperty
    entity.polygon.material = new Cesium.ColorMaterialProperty(color.withAlpha(alpha));

    // 更新当前高亮实体引用
    this.currentHighlightedEntity = entity;
  }

  /**
   * 取消区域高亮
   * @param entity 要取消高亮的实体
   */
  static unhighlightRegion(entity: Cesium.Entity): void {
    if (!entity || !entity.polygon || !entity.properties) return;

    // 恢复原始样式
    if (entity.properties.originalMaterial) {
      entity.polygon.material = entity.properties.originalMaterial;
    }

    // 如果当前高亮的实体是被取消高亮的实体，清除引用
    if (this.currentHighlightedEntity === entity) {
      this.currentHighlightedEntity = null;
    }
  }

  /**
   * 获取当前高亮的区域实体
   * @returns 当前高亮的区域实体，如果没有则返回null
   */
  static getCurrentHighlightedEntity(): Cesium.Entity | null {
    return this.currentHighlightedEntity;
  }
}

export default GeoJsonLoader;
